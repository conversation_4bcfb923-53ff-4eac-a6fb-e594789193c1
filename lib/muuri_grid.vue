<template>
  <div class="grid" :class="{ draggable }">
    <slot></slot>
  </div>
</template>
<script>
  export default {
    props: {
      draggable: Boolean,
      savedLayout: Array,
    },
  };
  // export default {
  //   props: {
  //     draggable: { type: <PERSON><PERSON><PERSON>, default: false },
  //     savedLayout: { type: Array, default: () => [] },
  //   },
  //   data: () => ({ grid: null }),
  //   async mounted() {
  //     await this.loadMuuri();
  //     this.initGrid();
  //   },
  //   beforeUnmount() {
  //     this.grid?.destroy();
  //   },
  //   methods: {
  //     async loadMuuri() {
  //       if (window.Muuri) return;

  //       const script = document.createElement("script");
  //       script.src =
  //         "https://cdn.jsdelivr.net/npm/muuri@0.9.5/dist/muuri.min.js";

  //       return new Promise((resolve, reject) => {
  //         script.onload = resolve;
  //         script.onerror = () => reject(new Error("Failed to load Mu<PERSON>"));
  //         document.head.appendChild(script);
  //       });
  //     },

  //     initGrid() {
  //       this.grid = new window.Muuri(this.$el, {
  //         items: ".item",
  //         dragEnabled: this.draggable,
  //         dragSortHeuristics: {
  //           sortInterval: 50,
  //           minDragDistance: 10,
  //           minBounceBackAngle: 1,
  //         },
  //         dragPlaceholder: {
  //           enabled: this.draggable,
  //           createElement: (item) => {
  //             const el = item.getElement().cloneNode(true);
  //             el.style.cssText = "opacity: 0.5; transform: scale(0.95)";
  //             return el;
  //           },
  //         },
  //         dragStartPredicate: { distance: 10, delay: 0 },
  //         layout: {
  //           fillGaps: false,
  //           horizontal: false,
  //           alignRight: false,
  //           alignBottom: false,
  //           rounding: true,
  //         },
  //       }).on(
  //         "move",
  //         this.$emit(
  //           "reorder",
  //           this.grid
  //             .getItems()
  //             .map((item) => item.getElement().getAttribute("data-index"))
  //         )
  //       );

  //       if (this.savedLayout?.length) {
  //         this.applySavedLayout(this.savedLayout);
  //       } else {
  //         this.grid.layout(true);
  //       }
  //     },

  //     serializeLayout() {
  //       return this.grid
  //         .getItems()
  //         .map((item) => item.getElement().getAttribute("data-index"));
  //     },

  //     applySavedLayout(savedOrder) {
  //       const currentItems = this.grid.getItems();
  //       const itemMap = new Map(
  //         currentItems.map((item) => [
  //           item.getElement().getAttribute("data-index"),
  //           item,
  //         ])
  //       );

  //       const newItems = savedOrder
  //         .map((id) => itemMap.get(id.toString()))
  //         .filter(Boolean);

  //       this.grid.sort(newItems, { layout: "instant" });
  //     },
  //   },
  // };
</script>
<style scoped>
  :scope > .grid {
    position: relative;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
  }

  :scope > .grid > .item {
    position: absolute;
    margin: 5px;
    z-index: 1;
    background: #000;
    border-radius: 8px;
    width: 180px;
    height: 120px;
  }

  :scope > .grid > .item.muuri-item-dragging {
    z-index: 3;
    transform: scale(1.05);
  }
  :scope > .grid > .item.muuri-item-releasing {
    z-index: 2;
  }
  :scope > .grid > .item.muuri-item-hidden {
    z-index: 0;
  }

  :scope > .grid > .item > .item-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  :scope > .grid.draggable > .item > .item-content {
    cursor: move;
  }

  :scope > .grid > .item > .item-content:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  :scope > .grid > .item > .item-content > .item-icon {
    font-size: 2rem;
    margin-bottom: 8px;
  }
  :scope > .grid > .item > .item-content > .item-label {
    font-size: 0.875rem;
    text-align: center;
  }
</style>
