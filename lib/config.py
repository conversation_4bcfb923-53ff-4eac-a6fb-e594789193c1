# Copyright (c) 2025 BoopBoard Project
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of
# this software and associated documentation files (the "Software"), to deal in
# the Software without restriction, including without limitation the rights to
# use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
# the Software, and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
# FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
# COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
# IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
"""Configuration management for BoopBoard using Pydantic Settings."""

from enum import StrEnum
from pathlib import Path
from typing import Annotated

from pydantic import BaseModel, Field
from pydantic_settings import (
    BaseSettings,
    JsonConfigSettingsSource,
    PydanticBaseSettingsSource,
)


class ButtonType(StrEnum):
    """Represents the type of button."""

    NORMAL = "normal"
    TOGGLE = "toggle"


class Button(BaseModel):
    """Represents a button with its visual and functional properties."""

    label: Annotated[str, Field("Boop", description="Display text on the button")]
    type: Annotated[
        ButtonType, Field(ButtonType.NORMAL, description="Button behavior type")
    ]
    command: Annotated[str, Field(..., description="Command to execute when pressed")]
    icon: Annotated[
        str, Field("radio_button_unchecked", description="Material icon name")
    ]
    color: Annotated[str, Field("#6366f1", description="Background color (hex)")]


class Layer(BaseModel):
    """Represents a layer containing buttons and their layout."""

    name: str
    icon: str
    buttons: list[Button]
    layout_order: Annotated[
        list[int], Field(default_factory=list, description="Visual order of buttons")
    ]


class Config(BaseSettings):
    """Main configuration using Pydantic Settings."""

    layers: Annotated[
        dict[str, Layer], Field(..., description="Named layers of buttons")
    ]

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        env_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        dotenv_settings: PydanticBaseSettingsSource,  # noqa: ARG003
        file_secret_settings: PydanticBaseSettingsSource,  # noqa: ARG003
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        """Load configuration from JSON file only."""
        return (JsonConfigSettingsSource(settings_cls, Path("config.json")),)

    def get_layer_buttons(self, layer_name: str = "main") -> list[Button]:
        """Get buttons for a specific layer in their original order."""
        return self.layers[layer_name].buttons

    def get_layer_layout_order(self, layer_name: str = "main") -> list[int]:
        """Get the saved layout order for a layer."""
        layer = self.layers[layer_name]
        return layer.layout_order or list(range(len(layer.buttons)))

    def get_default_layer(self) -> Layer:
        """Get the main/default layer."""
        return self.layers["main"]

    def save_layer_layout(self, new_order: list[int], layer_name: str = "main") -> None:
        """Save the layout order for a layer."""
        self.layers[layer_name].layout_order = new_order

    def save_to_file(self, file_path: str = "config.json") -> None:
        """Save the current configuration to a JSON file."""
        Path(file_path).write_text(self.model_dump_json(indent=4), encoding="utf-8")
