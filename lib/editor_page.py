# Copyright (c) 2025 BoopBoard Project
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of
# this software and associated documentation files (the "Software"), to deal in
# the Software without restriction, including without limitation the rights to
# use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
# the Software, and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
# FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
# COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
# IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
"""BoopBoard - a stream deck-style app, built with NiceG<PERSON>.

This module contains the editor page implementation, which is the interface
that the host will see when connecting to the app. It provides a way to
configure the macroboard and manage layers and buttons.
"""

from typing import Any

from nicegui import ui


def editor_page() -> None:
    """Editor page for configuring the macroboard."""
    ui.label("BoopBoard Editor").classes("text-2xl font-bold mb-4")

    # Add Muuri CSS and JavaScript from CDN
    ui.add_head_html("""
    <script src="https://cdn.jsdelivr.net/npm/muuri@0.9.5/dist/muuri.min.js"></script>
    """)

    # Add custom CSS for the grid and items
    ui.add_css("""
    .grid {
        position: relative;
        width: 100%;
        max-width: 800px;
        margin: 0 auto;
    }

    .item {
        position: absolute;
        display: block;
        margin: 5px;
        z-index: 1;
        background: #000;
        border-radius: 8px;
        width: 180px;
        height: 120px;
    }

    .item.muuri-item-dragging {
        z-index: 3;
        transform: scale(1.05);
    }

    .item.muuri-item-releasing {
        z-index: 2;
    }

    .item.muuri-item-hidden {
        z-index: 0;
    }

    .item-content {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        border-radius: 8px;
        cursor: move;
        transition: all 0.2s ease;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .item-content:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    .item-icon {
        font-size: 2rem;
        margin-bottom: 8px;
    }

    .item-label {
        font-size: 0.875rem;
        text-align: center;
    }
    """)

    # Create the draggable grid
    create_muuri_grid()


def create_muuri_grid() -> None:
    """Create a draggable grid using Muuri.js."""
    # Button data - this would normally come from config
    button_data = [
        {"label": "Button 1", "icon": "play_arrow", "color": "#3b82f6"},
        {"label": "Button 2", "icon": "pause", "color": "#10b981"},
        {"label": "Button 3", "icon": "stop", "color": "#ef4444"},
        {"label": "Button 4", "icon": "skip_next", "color": "#8b5cf6"},
        {"label": "Button 5", "icon": "volume_up", "color": "#eab308"},
        {"label": "Button 6", "icon": "volume_down", "color": "#ec4899"},
        {"label": "Button 7", "icon": "mic", "color": "#6366f1"},
        {"label": "Button 8", "icon": "mic_off", "color": "#6b7280"},
        {"label": "Button 9", "icon": "settings", "color": "#14b8a6"},
        {"label": "Button 10", "icon": "home", "color": "#f97316"},
        {"label": "Button 11", "icon": "star", "color": "#06b6d4"},
        {"label": "Button 12", "icon": "favorite", "color": "#f43f5e"},
        {"label": "Button 13", "icon": "lightbulb", "color": "#f59e0b"},
        {"label": "Button 14", "icon": "lock", "color": "#059669"},
        {"label": "Button 15", "icon": "wifi", "color": "#7c3aed"},
        {"label": "Button 16", "icon": "bluetooth", "color": "#64748b"},
    ]

    # Create container for the grid
    with ui.element("div").classes("w-full max-w-4xl mx-auto p-4"):
        ui.label("Drag and drop buttons to rearrange them").classes(
            "text-lg mb-6 text-center text-gray-700"
        )

        # Create the Muuri grid container
        with ui.element("div").classes("grid").props('id="grid"'):
            for i, button_info in enumerate(button_data):
                create_muuri_item(i, button_info)

    # Initialize Muuri grid
    ui.run_javascript("""
    // Wait for Muuri to be available
    function initMuuri() {
        if (typeof Muuri === 'undefined') {
            setTimeout(initMuuri, 100);
            return;
        }

        const grid = new Muuri('#grid', {
            items: '.item',
            dragEnabled: true,
            dragSortHeuristics: {
                sortInterval: 50,
                minDragDistance: 10,
                minBounceBackAngle: 1
            },
            dragPlaceholder: {
                enabled: true,
                createElement: function (item) {
                    const element = item.getElement().cloneNode(true);
                    element.style.opacity = '0.5';
                    element.style.transform = 'scale(0.95)';
                    return element;
                }
            },
            dragStartPredicate: {
                distance: 10,
                delay: 0
            },
            layout: {
                fillGaps: false,
                horizontal: false,
                alignRight: false,
                alignBottom: false,
                rounding: true
            }
        });

        // Handle drag end event
        grid.on('dragEnd', function (item, event) {
            console.log('Item moved:', item.getElement().dataset.index);
            // Here you could send the new order to the backend
            const items = grid.getItems();
            const newOrder = items.map(item => item.getElement().dataset.index);
            console.log('New order:', newOrder);
        });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initMuuri);
    } else {
        initMuuri();
    }
    """)


def create_muuri_item(index: int, button_info: dict[str, Any]) -> None:
    """Create a single Muuri grid item."""
    with (
        ui.element("div").classes("item").props(f'data-index="{index}"'),
        (
            ui.element("div")
            .classes("item-content")
            .style(f"background-color: {button_info['color']}")
        ),
    ):
        # Add icon
        ui.icon(button_info["icon"]).classes("item-icon")
        # Add label
        ui.label(button_info["label"]).classes("item-label")
