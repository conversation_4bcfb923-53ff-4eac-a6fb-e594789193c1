# Copyright (c) 2025 BoopBoard Project
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of
# this software and associated documentation files (the "Software"), to deal in
# the Software without restriction, including without limitation the rights to
# use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
# the Software, and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
# FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
# COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
# IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
"""BoopBoard - a stream deck-style app, built with NiceG<PERSON>.

This module contains the macro page implementation, which is the main interface
that clients see when connecting to the app.
"""

from nicegui import ui


def macro_page() -> None:
    """Macro page that the client will see."""
    fullscreen = ui.fullscreen()

    # Prevent pull-to-refresh and overscroll on touch
    ui.add_css("""
    body, html, #nicegui-body {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        height: 100% !important;
    }

    body {
        overscroll-behavior: none !important;
    }
    """)

    # Prompt user to go full screen for better experience
    if not fullscreen.value:

        def _make_fullscreen() -> None:
            fullscreen.enter()
            dialog.close()
            dialog.clear()

        with ui.dialog().props("persistent") as dialog, ui.card():
            ui.label("Please go full screen to use this app.")
            ui.button("OK", on_click=_make_fullscreen)
        dialog.open()

    # Main Layout
    with ui.element("div").classes("flex w-full h-screen overflow-hidden"):
        # Layer Sidebar
        with ui.element("div").classes(
            "w-16 bg-base-300 p-2 flex flex-col items-center justify-center gap-4"
        ):
            # Default Layer
            ui.icon("view_quilt").classes("text-4xl text-white")

        # Main Content
        with (
            ui.element("div").classes(
                "grow bg-base-100 pb-4 flex flex-col"
                "overflow-hidden justify-items-center"
            ),
            ui.grid(rows=4, columns=4).classes("w-full h-full gap-2 p-4"),
        ):
            for i in range(1, 17):  # 4x4 = 16 buttons
                ui.button(f"Button {i}").classes("w-full h-full min-h-0")
