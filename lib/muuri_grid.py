from collections.abc import Callable

from nicegui import ui
from nicegui.events import GenericEventArguments


class Muuri(ui.element, component="muuri_grid.vue", dependencies=["muuri.min.js"]):
    """Custom Muuri grid element with drag-and-drop functionality."""

    def __init__(
        self,
        *,
        draggable: bool = False,
        on_reorder: Callable[[list[int]], None] | None = None,
        saved_layout: list[int] | None = None,
    ) -> None:
        """Initialize the Muuri grid.

        Args:
            draggable: Whether items can be dragged and reordered
            on_reorder: Callback function called when items are reordered
            saved_layout: Previously saved layout order to restore

        """
        super().__init__()
        self.props["draggable"] = draggable
        self.props["savedLayout"] = saved_layout or []
        self._on_reorder_callback = on_reorder
        self.on("reorder", self._handle_reorder)

    def _handle_reorder(self, event: GenericEventArguments) -> None:
        """Handle the reorder event from Vue component."""
        if self._on_reorder_callback and event.args:
            new_order = [int(x) for x in event.args]
            self._on_reorder_callback(new_order)
