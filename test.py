from nicegui import ui

from lib.config import Config
from lib.muuri_grid import <PERSON><PERSON>


@ui.page("/")
def main() -> None:
    """Main page demonstrating draggable grid layout."""
    is_host = ui.context.client.ip == "127.0.0.1"
    config = Config()
    buttons = config.get_layer_buttons()

    # UI Header
    ui.label("Draggable Grid Layout").classes("text-3xl font-bold text-center mb-6")
    ui.label("Drag and drop the buttons to rearrange them").classes(
        "text-center text-gray-600 mb-8"
    )

    # Status display
    status_label = ui.label("Ready").classes("text-center text-green-600 mb-4")

    def update_status(message: str, *, is_error: bool = False) -> None:
        """Update status label with message and color."""
        status_label.text = message
        color_class = "text-red-600" if is_error else "text-green-600"
        status_label.classes(f"text-center {color_class} mb-4")

    def manual_save() -> None:
        """Manually save the current configuration."""
        try:
            config.save_to_file()
            update_status("✅ Configuration saved manually")
        except (OSError, ValueError) as e:
            update_status(f"❌ Error saving: {e!s}", is_error=True)

    ui.button("Save Configuration", on_click=manual_save).classes("mb-4")

    def handle_reorder(new_order: list[int]) -> None:
        """Handle button reordering and save to config."""
        try:
            config.save_layer_layout(new_order)
            config.save_to_file()
            config.__init__()  # noqa: PLC2801
            update_status(f"✅ Saved new layout: {new_order}")
        except (OSError, ValueError) as e:
            update_status(f"❌ Error saving: {e!s}", is_error=True)

    # Create grid with saved layout
    saved_layout = config.get_layer_layout_order()

    with Muuri(draggable=is_host, on_reorder=handle_reorder, saved_layout=saved_layout):
        for i, button in enumerate(buttons):
            with (
                ui.element("div").classes("item").props(f'data-index="{i}"'),
                ui.element("div")
                .classes("item-content")
                .style(f"background-color: {button.color}"),
            ):
                ui.icon(button.icon).classes("item-icon")
                ui.label(button.label).classes("item-label")


ui.run(
    port=8080,
    dark=None,
    title="BoopBoard",
    favicon="🗿",
)
