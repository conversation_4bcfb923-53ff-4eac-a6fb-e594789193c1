[project]
name = "boopboard"
version = "0.1.0"
description = "A simple, customizable, and easy to use macroboard"
readme = "README.md"
requires-python = ">=3.13"
dependencies = ["nicegui>=2.20.0", "pydantic-settings>=2.9.1"]

[project.gui-scripts]
boopboard = "main.py"

[tool.ruff]
fix = true
line-length = 88
cache-dir = "/tmp/ruff_cache"

[tool.ruff.lint]
select = ["ALL"]
extend-ignore = [
    "COM812",  # missing trailing comma
    "D100",    # missing docstring in public module
    "D401",    # first line should be in imperative mood
    "DOC201",  # missing `return` in docstring
    "PLR0913", # too many arguments to function call
    "TD003",   # missing TODO issue link
]

[tool.mypy]
plugins = "pydantic.mypy"
cache_dir = "/tmp/mypy_cache"
strict = true
