# Copyright (c) 2025 BoopBoard Project
#
# Permission is hereby granted, free of charge, to any person obtaining a copy of
# this software and associated documentation files (the "Software"), to deal in
# the Software without restriction, including without limitation the rights to
# use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
# the Software, and to permit persons to whom the Software is furnished to do so,
# subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
# FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
# COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
# IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
# CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
"""BoopBoard - a stream deck-style app, built with NiceG<PERSON>.

This module contains the main page implementation, which is the entry point
for the app. It checks if the client is the host and displays the appropriate
page.
"""

from nicegui import native, ui

from lib import Config, editor_page, macro_page

config = Config()


@ui.page("/")
def main() -> None:
    """Main page of the app, which is either the macro or editor page."""
    is_host = ui.context.client.ip == "127.0.0.1"

    if is_host:
        editor_page()
    else:
        macro_page()


ui.run(
    port=native.find_open_port(),  # Finds an open port for the app to use
    dark=None,
    title="BoopBoard",
    favicon="🗿",
)
